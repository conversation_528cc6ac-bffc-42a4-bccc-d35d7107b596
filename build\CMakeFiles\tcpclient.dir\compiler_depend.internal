# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

CMakeFiles/tcpclient.dir/src/tcpclient.cpp.obj
 D:/AAVM/Http_Client/src/tcpclient.cpp
 D:/AAVM/Http_Client/include/XTcp/XTcp.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/backward/binders.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bit
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/alloc_traits.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/allocator.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_ios.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_ios.tcc
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_string.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/basic_string.tcc
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/char_traits.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/charconv.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/concept_check.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/cpp_type_traits.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/cxxabi_forced.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/cxxabi_init_exception.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/exception.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/exception_defines.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/exception_ptr.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/functexcept.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/functional_hash.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/hash_bytes.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/invoke.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ios_base.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/istream.tcc
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_classes.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_classes.tcc
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_facets.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/locale_facets.tcc
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/localefwd.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/memory_resource.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/memoryfwd.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/move.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/nested_exception.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/new_allocator.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ostream.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ostream.tcc
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ostream_insert.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/postypes.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/predefined_ops.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/ptr_traits.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/range_access.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/refwrap.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/requires_hosted.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/std_abs.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_algobase.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_construct.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_function.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_iterator.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_iterator_base_funcs.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_iterator_base_types.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stl_pair.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/streambuf.tcc
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/streambuf_iterator.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/string_view.tcc
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/stringfwd.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/uses_allocator.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/uses_allocator_args.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/utility.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/bits/version.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cctype
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cerrno
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/clocale
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/concepts
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cstddef
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cstdio
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cstdlib
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cwchar
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/cwctype
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/debug/assertions.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/debug/debug.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/exception
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/alloc_traits.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/atomicity.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/numeric_traits.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/string_conversions.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ext/type_traits.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/initializer_list
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ios
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/iosfwd
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/iostream
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/istream
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/new
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/ostream
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/pstl/pstl_config.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/stdexcept
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/stdlib.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/streambuf
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/string
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/string_view
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/system_error
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/tuple
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/type_traits
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/typeinfo
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/atomic_word.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/c++allocator.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/c++config.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/c++locale.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/cpu_defines.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/ctype_base.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/ctype_inline.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/error_constants.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/gthr-default.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/gthr.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/c++/x86_64-w64-mingw32/bits/os_defines.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/limits.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/mm_malloc.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/stddef.h
 D:/AAVM/software/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include/syslimits.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/_mingw.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/_mingw_mac.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/_mingw_off_t.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/_mingw_secapi.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/_mingw_stat64.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/corecrt.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/corecrt_stdio_config.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/corecrt_wstdlib.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/crtdefs.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/ctype.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/errno.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/limits.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/locale.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/malloc.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/sdks/_mingw_ddk.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/sec_api/stdio_s.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/sec_api/stdlib_s.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/sec_api/sys/timeb_s.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/sec_api/wchar_s.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/stddef.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/stdio.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/stdlib.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/swprintf.inl
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/sys/timeb.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/vadefs.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/wchar.h
 D:/AAVM/software/mingw64/x86_64-w64-mingw32/include/wctype.h

