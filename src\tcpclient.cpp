#include "XTcp.h"
#include <iostream>
#include <string>

int main(){
    std::cout << "TCP客户端启动..." << std::endl;

    XTcp client;

    // 创建套接字
    std::cout << "创建套接字..." << std::endl;
    int result = client.CreateSocket();
    if (result <= 0) {
        std::cerr << "创建套接字失败！错误代码: " << result << std::endl;
        std::cout << "按任意键退出..." << std::endl;
        std::cin.get();
        return -1;
    }
    std::cout << "套接字创建成功，句柄: " << result << std::endl;

    // 连接到服务器
    std::cout << "正在连接到服务器 ************:8080..." << std::endl;
    bool connected = client.Connect("************", 8080);
    if (!connected) {
        std::cerr << "连接服务器失败！" << std::endl;
        std::cout << "请检查：" << std::endl;
        std::cout << "1. 服务器是否正在运行" << std::endl;
        std::cout << "2. IP地址和端口是否正确" << std::endl;
        std::cout << "3. 网络连接是否正常" << std::endl;
        client.Close();
        std::cout << "按任意键退出..." << std::endl;
        std::cin.get();
        return -1;
    }

    std::cout << "连接成功！" << std::endl;

    // 发送测试数据
    std::string message = "Hello from TCP Client!";
    std::cout << "发送消息: " << message << std::endl;
    int sent = client.Send(message.c_str(), message.length());
    if (sent > 0) {
        std::cout << "发送成功，发送了 " << sent << " 字节" << std::endl;
    } else {
        std::cerr << "发送失败！" << std::endl;
    }

    // 接收响应
    char buffer[1024] = {0};
    std::cout << "等待服务器响应..." << std::endl;
    int received = client.Recv(buffer, sizeof(buffer) - 1);
    if (received > 0) {
        buffer[received] = '\0';
        std::cout << "收到响应: " << buffer << std::endl;
    } else if (received == 0) {
        std::cout << "服务器关闭了连接" << std::endl;
    } else {
        std::cerr << "接收数据失败！" << std::endl;
    }

    // 关闭连接
    std::cout << "关闭连接..." << std::endl;
    client.Close();

    std::cout << "程序执行完成！" << std::endl;
    std::cout << "按任意键退出..." << std::endl;
    std::cin.get();

    return 0;
}

