#include "../include/XTcp/XTcp.h"
#include <iostream>
#ifdef WIN32
#include <winsock2.h>
#include <ws2tcpip.h>
#pragma comment(lib, "ws2_32.lib")
#endif

int main(){
    std::cout << "TCP客户端启动..." << std::endl;

#ifdef WIN32
    // 初始化 Winsock
    WSADATA wsaData;
    int wsaResult = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (wsaResult != 0) {
        std::cerr << "WSAStartup 失败，错误代码: " << wsaResult << std::endl;
        std::cout << "按任意键退出..." << std::endl;
        std::cin.get();
        return -1;
    }
    std::cout << "Winsock 初始化成功" << std::endl;
#endif

    XTcp client;

    // 创建套接字
    std::cout << "创建套接字..." << std::endl;
    int result = client.CreateSocket();
    if (result <= 0) {
        std::cerr << "创建套接字失败！错误代码: " << result << std::endl;
#ifdef WIN32
        std::cerr << "WSA错误代码: " << WSAGetLastError() << std::endl;
#endif
        std::cout << "按任意键退出..." << std::endl;
        std::cin.get();
        return -1;
    }
    std::cout << "套接字创建成功，句柄: " << result << std::endl;

    // 连接到服务器
    std::cout << "正在连接到服务器 ************:8080..." << std::endl;
    bool connected = client.Connect("************",8080);
    if (!connected) {
        std::cerr << "连接服务器失败！" << std::endl;
#ifdef WIN32
        int error = WSAGetLastError();
        std::cerr << "WSA错误代码: " << error << std::endl;
        switch(error) {
            case WSAECONNREFUSED:
                std::cerr << "连接被拒绝 - 服务器可能没有在监听该端口" << std::endl;
                break;
            case WSAENETUNREACH:
                std::cerr << "网络不可达" << std::endl;
                break;
            case WSAETIMEDOUT:
                std::cerr << "连接超时" << std::endl;
                break;
            case WSAEHOSTUNREACH:
                std::cerr << "主机不可达" << std::endl;
                break;
            default:
                std::cerr << "其他网络错误" << std::endl;
                break;
        }
#endif
        std::cout << "请检查：" << std::endl;
        std::cout << "1. 服务器是否正在运行并监听端口 8080" << std::endl;
        std::cout << "2. IP地址 ************ 是否正确" << std::endl;
        std::cout << "3. 防火墙是否阻止了连接" << std::endl;
        std::cout << "4. 网络连接是否正常" << std::endl;

        client.Close();
        std::cout << "按任意键退出..." << std::endl;
        std::cin.get();
        return -1;
    }

    std::cout << "连接成功！" << std::endl;

    // 发送测试消息
    std::string testMsg = "Hello Server!";
    std::cout << "发送测试消息: " << testMsg << std::endl;
    int sent = client.Send(testMsg.c_str(), testMsg.length());
    if (sent > 0) {
        std::cout << "发送成功，发送了 " << sent << " 字节" << std::endl;
    } else {
        std::cerr << "发送失败" << std::endl;
    }

    // 关闭连接
    client.Close();
    std::cout << "程序执行完成！按任意键退出..." << std::endl;
    std::cin.get();

#ifdef WIN32
    WSACleanup();
#endif

    return 0;
}

