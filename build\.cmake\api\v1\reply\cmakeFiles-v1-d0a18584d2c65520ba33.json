{"inputs": [{"path": "CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeSystem.cmake.in"}, {"isGenerated": true, "path": "build/CMakeFiles/4.1.0/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeMinGWFindMake.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-Initialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-Determine-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeCompilerIdDetection.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/ADSP-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/ARMCC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/ARMClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/AppleClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Borland-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Clang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Cray-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/CrayClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Diab-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/GHS-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/IAR-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Intel-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/MSVC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/NVHPC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/OrangeC-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/PGI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/PathScale-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Renesas-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/SCO-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/TI-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/TIClang-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Tasking-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Watcom-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "build/CMakeFiles/4.1.0/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeRCCompiler.cmake.in"}, {"isGenerated": true, "path": "build/CMakeFiles/4.1.0/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-windres.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeTestRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeParseImplicitIncludeInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeParseImplicitLinkInfo.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeParseLibraryArchitecture.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeTestCompilerCommon.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeCXXCompilerABI.cpp"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerSupport.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Internal/FeatureTesting.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeCXXCompiler.cmake.in"}, {"isGenerated": true, "path": "build/CMakeFiles/4.1.0/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-GNU-CXX-ABI.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Internal/CMakeCXXLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Linker/Windows-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Linker/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Internal/CMakeInspectCXXLinker.cmake"}, {"isCMake": true, "isExternal": true, "path": "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeCXXCompiler.cmake.in"}], "kind": "cmakeFiles", "paths": {"build": "D:/AAVM/Http_Client/build", "source": "D:/AAVM/Http_Client"}, "version": {"major": 1, "minor": 1}}