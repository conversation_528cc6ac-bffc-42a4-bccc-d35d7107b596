# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = D:\C++source\Tools\Cmake\bin\cmake.exe

# The command to remove a file.
RM = D:\C++source\Tools\Cmake\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\AAVM\Http_Client

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\AAVM\Http_Client\build

# Include any dependencies generated for this target.
include CMakeFiles/tcpclient.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/tcpclient.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/tcpclient.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/tcpclient.dir/flags.make

CMakeFiles/tcpclient.dir/codegen:
.PHONY : CMakeFiles/tcpclient.dir/codegen

CMakeFiles/tcpclient.dir/src/tcpclient.cpp.obj: CMakeFiles/tcpclient.dir/flags.make
CMakeFiles/tcpclient.dir/src/tcpclient.cpp.obj: CMakeFiles/tcpclient.dir/includes_CXX.rsp
CMakeFiles/tcpclient.dir/src/tcpclient.cpp.obj: D:/AAVM/Http_Client/src/tcpclient.cpp
CMakeFiles/tcpclient.dir/src/tcpclient.cpp.obj: CMakeFiles/tcpclient.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=D:\AAVM\Http_Client\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/tcpclient.dir/src/tcpclient.cpp.obj"
	D:\AAVM\software\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/tcpclient.dir/src/tcpclient.cpp.obj -MF CMakeFiles\tcpclient.dir\src\tcpclient.cpp.obj.d -o CMakeFiles\tcpclient.dir\src\tcpclient.cpp.obj -c D:\AAVM\Http_Client\src\tcpclient.cpp

CMakeFiles/tcpclient.dir/src/tcpclient.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/tcpclient.dir/src/tcpclient.cpp.i"
	D:\AAVM\software\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E D:\AAVM\Http_Client\src\tcpclient.cpp > CMakeFiles\tcpclient.dir\src\tcpclient.cpp.i

CMakeFiles/tcpclient.dir/src/tcpclient.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/tcpclient.dir/src/tcpclient.cpp.s"
	D:\AAVM\software\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S D:\AAVM\Http_Client\src\tcpclient.cpp -o CMakeFiles\tcpclient.dir\src\tcpclient.cpp.s

# Object files for target tcpclient
tcpclient_OBJECTS = \
"CMakeFiles/tcpclient.dir/src/tcpclient.cpp.obj"

# External object files for target tcpclient
tcpclient_EXTERNAL_OBJECTS =

bin/tcpclient.exe: CMakeFiles/tcpclient.dir/src/tcpclient.cpp.obj
bin/tcpclient.exe: CMakeFiles/tcpclient.dir/build.make
bin/tcpclient.exe: D:/AAVM/Http_Client/lib/libXTcp.dll.a
bin/tcpclient.exe: CMakeFiles/tcpclient.dir/linkLibs.rsp
bin/tcpclient.exe: CMakeFiles/tcpclient.dir/objects1.rsp
bin/tcpclient.exe: CMakeFiles/tcpclient.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=D:\AAVM\Http_Client\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Linking CXX executable bin\tcpclient.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\tcpclient.dir\link.txt --verbose=$(VERBOSE)
	D:\C++source\Tools\Cmake\bin\cmake.exe -E copy_if_different D:/AAVM/Http_Client/lib/libXTcp.dll D:/AAVM/Http_Client/build/bin

# Rule to build all files generated by this target.
CMakeFiles/tcpclient.dir/build: bin/tcpclient.exe
.PHONY : CMakeFiles/tcpclient.dir/build

CMakeFiles/tcpclient.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\tcpclient.dir\cmake_clean.cmake
.PHONY : CMakeFiles/tcpclient.dir/clean

CMakeFiles/tcpclient.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" D:\AAVM\Http_Client D:\AAVM\Http_Client D:\AAVM\Http_Client\build D:\AAVM\Http_Client\build D:\AAVM\Http_Client\build\CMakeFiles\tcpclient.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/tcpclient.dir/depend

