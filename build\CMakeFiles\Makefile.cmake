# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "MinGW Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "D:/AAVM/Http_Client/CMakeLists.txt"
  "CMakeFiles/4.1.0/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeRCCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeSystem.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeCXXCompiler.cmake.in"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeCXXCompilerABI.cpp"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeCXXInformation.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeCommonLanguageInclude.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeCompilerIdDetection.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCXXCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerABI.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerId.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineCompilerSupport.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineRCCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeDetermineSystem.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeFindBinUtils.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeGenericSystem.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeInitializeConfigs.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeLanguageInformation.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeMinGWFindMake.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeParseImplicitLinkInfo.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeParseLibraryArchitecture.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeRCCompiler.cmake.in"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeRCInformation.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeSystem.cmake.in"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeSystemSpecificInformation.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeSystemSpecificInitialize.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeTestCXXCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeTestCompilerCommon.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/CMakeTestRCCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/CrayClang-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Diab-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/GNU-CXX.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/GNU-FindBinUtils.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/GNU.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/NVHPC-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/OrangeC-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Renesas-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/TI-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/TIClang-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Tasking-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Internal/CMakeCXXLinkerInformation.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Internal/CMakeDetermineLinkerId.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Internal/CMakeInspectCXXLinker.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Internal/FeatureTesting.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Linker/GNU.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Linker/Windows-CXX.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU-CXX.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-Determine-CXX.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-GNU-CXX-ABI.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-GNU-CXX.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-GNU.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-Initialize.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows-windres.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/Windows.cmake"
  "D:/C++source/Tools/Cmake/share/cmake-4.1/Modules/Platform/WindowsPaths.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/4.1.0/CMakeSystem.cmake"
  "CMakeFiles/4.1.0/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeRCCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeCXXCompiler.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/tcpclient.dir/DependInfo.cmake"
  )
