# CMake最低版本要求
cmake_minimum_required(VERSION 3.16)

# 项目名称和版本
project(Http_Client VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# 包含头文件目录
include_directories(${CMAKE_SOURCE_DIR}/include)
include_directories(${CMAKE_SOURCE_DIR}/include/XTcp)

# 查找已编译的XTcp库
find_library(XTCP_LIBRARY
    NAMES libXTcp XTcp
    PATHS ${CMAKE_SOURCE_DIR}/lib
    NO_DEFAULT_PATH
)

if(NOT XTCP_LIBRARY)
    message(FATAL_ERROR "XTcp library not found in lib directory")
endif()

# Windows特定设置
if(WIN32)
    # 设置Windows socket库
    set(PLATFORM_LIBS ws2_32 wsock32)

    # 复制DLL到输出目录的函数
    function(copy_dll_to_output target_name)
        add_custom_command(TARGET ${target_name} POST_BUILD
            COMMAND ${CMAKE_COMMAND} -E copy_if_different
            "${CMAKE_SOURCE_DIR}/lib/libXTcp.dll"
            $<TARGET_FILE_DIR:${target_name}>
        )
    endfunction()
else()
    set(PLATFORM_LIBS pthread)
endif()

# 创建TCP客户端可执行文件
add_executable(tcpclient
    src/tcpclient.cpp
)

# 链接库
target_link_libraries(tcpclient
    ${XTCP_LIBRARY}
    ${PLATFORM_LIBS}
)

# Windows下复制DLL
if(WIN32)
    copy_dll_to_output(tcpclient)
endif()

# 设置编译选项
if(MSVC)
    target_compile_options(tcpclient PRIVATE /W4)
else()
    target_compile_options(tcpclient PRIVATE -Wall -Wextra -pedantic)
endif()

# 安装规则
install(TARGETS tcpclient
    RUNTIME DESTINATION bin
)

if(WIN32)
    install(FILES ${CMAKE_SOURCE_DIR}/lib/libXTcp.dll
        DESTINATION bin
    )
endif()

# 测试支持
enable_testing()

# 如果tests目录有测试文件，可以添加测试
file(GLOB TEST_SOURCES "tests/*.cpp")
if(TEST_SOURCES)
    foreach(test_source ${TEST_SOURCES})
        get_filename_component(test_name ${test_source} NAME_WE)
        add_executable(${test_name} ${test_source})
        target_link_libraries(${test_name}
            ${XTCP_LIBRARY}
            ${PLATFORM_LIBS}
        )
        if(WIN32)
            copy_dll_to_output(${test_name})
        endif()
        add_test(NAME ${test_name} COMMAND ${test_name})
    endforeach()
endif()

# 打印配置信息
message(STATUS "Project: ${PROJECT_NAME}")
message(STATUS "Version: ${PROJECT_VERSION}")
message(STATUS "C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "XTcp Library: ${XTCP_LIBRARY}")
message(STATUS "Platform Libraries: ${PLATFORM_LIBS}")