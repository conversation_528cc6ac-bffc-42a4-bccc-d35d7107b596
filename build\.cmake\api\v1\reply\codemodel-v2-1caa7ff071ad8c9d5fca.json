{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-f9592f6d8850f47f6d3f.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Http_Client", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "tcpclient::@6890427a1f51a3e7e1df", "jsonFile": "target-tcpclient-Debug-3308a284a2a6c252db80.json", "name": "tcpclient", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/AAVM/Http_Client/build", "source": "D:/AAVM/Http_Client"}, "version": {"major": 2, "minor": 8}}